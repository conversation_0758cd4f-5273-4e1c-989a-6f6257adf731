package com.example.aimusicplayer.utils

import androidx.media3.common.MediaItem
import androidx.recyclerview.widget.DiffUtil
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.PlayList


/**
 * DiffCallbacks工具类
 * 提供各种列表差异比较回调，用于优化RecyclerView更新
 */
object DiffCallbacks {

    /**
     * 评论列表差异比较回调
     * 用于优化评论列表的更新，减少不必要的重绘
     */
    class CommentDiffCallback(
        private val oldComments: List<Comment>,
        private val newComments: List<Comment>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldComments.size

        override fun getNewListSize(): Int = newComments.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较评论ID是否相同
            return oldComments[oldItemPosition].commentId == newComments[newItemPosition].commentId
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较评论内容是否相同
            val oldComment = oldComments[oldItemPosition]
            val newComment = newComments[newItemPosition]
            return oldComment.content == newComment.content &&
                   oldComment.username == newComment.username &&
                   oldComment.likeCount == newComment.likeCount
        }
    }

    /**
     * 播放列表差异比较回调（PlayList版本）
     * 用于优化播放列表的更新，减少不必要的重绘
     */
    class PlaylistDiffCallback(
        private val oldPlaylists: List<PlayList>,
        private val newPlaylists: List<PlayList>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldPlaylists.size

        override fun getNewListSize(): Int = newPlaylists.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较播放列表ID是否相同
            return oldPlaylists[oldItemPosition].id == newPlaylists[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较播放列表内容是否相同
            val oldPlaylist = oldPlaylists[oldItemPosition]
            val newPlaylist = newPlaylists[newItemPosition]
            return oldPlaylist.name == newPlaylist.name &&
                   oldPlaylist.coverImgUrl == newPlaylist.coverImgUrl &&
                   oldPlaylist.playCount == newPlaylist.playCount
        }
    }

    /**
     * 媒体项列表差异比较回调
     * 用于优化播放列表的更新，减少不必要的重绘
     */
    class MediaItemDiffCallback(
        private val oldMediaItems: List<MediaItem>,
        private val newMediaItems: List<MediaItem>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldMediaItems.size

        override fun getNewListSize(): Int = newMediaItems.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较媒体项ID是否相同
            val oldItem = oldMediaItems[oldItemPosition]
            val newItem = newMediaItems[newItemPosition]
            // MediaItem的mediaId属性是非null的，直接比较
            return oldItem.mediaId == newItem.mediaId
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldMediaItems[oldItemPosition]
            val newItem = newMediaItems[newItemPosition]

            // 比较标题、艺术家和专辑
            val titleSame = oldItem.mediaMetadata.title == newItem.mediaMetadata.title
            val artistSame = oldItem.mediaMetadata.artist == newItem.mediaMetadata.artist
            val albumSame = oldItem.mediaMetadata.albumTitle == newItem.mediaMetadata.albumTitle

            return titleSame && artistSame && albumSame
        }
    }
}


