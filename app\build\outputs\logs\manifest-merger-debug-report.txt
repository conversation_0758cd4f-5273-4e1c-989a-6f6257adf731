-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:105:9-113:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:109:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:107:13-60
	android:exported
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:108:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:106:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:2:1-116:12
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:2:1-116:12
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:2:1-116:12
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:2:1-116:12
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6544556cf1674494534f8900bce21c07\transformed\viewbinding-8.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3e619597e4c9cd618216bba15d9c65\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6cb25c545573f419954e68b8ad06ac8\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6775c4e0d2ccafaa99b9198dc5c7bbc7\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3c414797dbc39db6a242631605ef8e6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\752d364363cfe83d07ebd08f1853b21e\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3929c15ecd3ed3bcbd327761aaa90f30\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be6552077492e037cb648ba9d20c50b2\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f4bdb46ff28ab587a34c53d6687bf99\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e927d56ef561df1431f9c8adb12ddc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92121f0061d84fd6603428dd9555221c\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.50] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8dbd27110790d4450617052df3c5d93\transformed\hilt-android-2.50\AndroidManifest.xml:16:1-19:12
MERGED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:17:1-56:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42d76bf5c058c9faeb6229240c83266\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:2:1-15:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80574656206597bee036fe941ba428c7\transformed\glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd8b03a9aacee39a4f36b236dc8c1b64\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af85d7b532fa32807b254d6290ddfca0\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268dd332085b36047c7c8b393feff7e2\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cf6da45ccd670ee21b434999efbd41\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea0d4e8623464af29dca49a272472225\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d303f1f0d1dfdf89d203456ad79266\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78bf313ee09e26c8c671a0bbc2457ec1\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1916aa273d557d541d17bc9f12ca7920\transformed\media3-session-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4caf9741b7f09b99a2a3a0bd3b3d7c6b\transformed\media3-datasource-okhttp-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb7207166b230fc420f90518c02881cc\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195bf0261f5accce3675bff17e890464\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c21fd9d5c777f873383f9d1179d8ce8\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b148c17468b063643693b5fb41c2c086\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a26edc275529aa4f7cf05aefcad03d8e\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.palette:palette:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b92fa3efd2e831e33070469b4d31f135\transformed\palette-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040370f8816f4299de3bc74ca2c211f0\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af754651ec5d146d4692fd33a60cd6b5\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a822c4c17d161f53845a19fb128f54e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5877e86b535d8beee8141860616f120d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ecf52e2c51c09fa887ff15e8044d4a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b127eabf3fe07a7bca176f62ec5e72c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bf41a125552eeedf1dc74cafa954fee\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424b875497a500c66d36b320a185aeee\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc26e2c2b777fa3787802ee43f5584c7\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\422bad9bd1fca6b358e7b95759e82cab\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424fca0debd1544e67e0c0822c21d9e7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c6550fdb6c628b24ce9db4a741be8\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\518fe953358109ce0c8063a6625d5c4f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8b2f4de0ad0aa176dba9826c660dea7\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55d4dce1a1d094ee0ef77033f8ed8372\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a46b3cd1f3124b42b4d032df4219956e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a33d50cf3518ee712f7b48e2658100\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a572985955c753a461ab551d4cdef229\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b859aed4ff87e5f7f2564514b13edb6\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25a5afb01185fcabb33098703e48850c\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\452a3d61bd5a4df9c7b891c073bc51a8\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d18e326a7c95950bec51507cc408934\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f63136d98328a54f1dd0cb208b70f66f\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30a67fdc3d1b95d3c56f9db8b33f6a04\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e79bbf8a7b26e1175d35315dd720102f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b36e363eec877c898c6a6b798c8febc9\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f62e4a4d8a407c6b06d9e47fff0e003\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e490ace83d3e445f58e57bcd5ec1a3\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe9baeb1697de8666b4cdbdc3b7b7ec\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c6aba8d898bf5afa2792c4cbcbb6b8\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a53f08fca904f0165cea08f11ecb38\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9eef6ad673a264cc89263a38493d29db\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794c77caa217dc49438dbea4b18de558\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185d612fe844270dbe94dbecbf717d3b\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2b4430280827f7182af831eee6980fb\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667cc5877b26e9c23bb3ff4b31c492\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3fffdf45786e3c2b6c69e86201b2a80\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e0bcd8e0ccc9c7bfaf57677d7c3ed9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62ea7150938e602811be82533a7e3ed\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\661458eafcd301f339addf1cd11d3f67\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ce6023b12293a411384d5c2c100e77\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40b9f263e73af9f18fde6d0035bd27ff\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f71c86c6eabe71e98ff6abdb70ecc14\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c376439633ee4e11e579bd0dba6d6e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e0af7e6772353ecb8c2a0e492de2ce7\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.dagger:dagger-lint-aar:2.50] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff1bdd17c51c51bdc66035dbacd7b9c\transformed\dagger-lint-aar-2.50\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:5-78
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:15:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:22-72
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:22-72
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:5-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:22-68
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:22-76
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:22-72
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:22-75
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:22-65
application
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:40:5-114:19
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:40:5-114:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:9:5-13:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0\AndroidManifest.xml:9:5-20
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:49:9-52
	android:largeHeap
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:53:9-33
	android:icon
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:45:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:51:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:47:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:46:9-27
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:52:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:44:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:54:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:42:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:48:9-51
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:43:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:50:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:41:9-41
meta-data#com.baidu.speech.APP_ID
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:57:9-59:41
	android:value
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:59:13-38
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:58:13-51
meta-data#com.baidu.speech.API_KEY
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:60:9-62:56
	android:value
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:62:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:61:13-52
meta-data#com.baidu.speech.SECRET_KEY
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:63:9-65:64
	android:value
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:65:13-61
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:64:13-55
activity#com.example.aimusicplayer.ui.splash.SplashActivity
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:68:9-78:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:71:13-50
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:73:13-47
	android:exported
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:72:13-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:74:13-77:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:75:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:75:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:76:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:76:27-74
activity#com.example.aimusicplayer.ui.login.LoginActivity
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:81:9-86:50
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:84:13-50
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:86:13-47
	android:exported
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:83:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:85:13-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:82:13-51
activity#com.example.aimusicplayer.ui.main.MainActivity
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:89:9-94:50
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:92:13-50
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:94:13-47
	android:exported
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:91:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:93:13-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:90:13-49
service#com.example.aimusicplayer.service.UnifiedPlaybackService
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:97:9-100:40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:99:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:100:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:98:13-59
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:110:13-112:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:112:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:111:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6544556cf1674494534f8900bce21c07\transformed\viewbinding-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6544556cf1674494534f8900bce21c07\transformed\viewbinding-8.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3e619597e4c9cd618216bba15d9c65\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3e619597e4c9cd618216bba15d9c65\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6cb25c545573f419954e68b8ad06ac8\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6cb25c545573f419954e68b8ad06ac8\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6775c4e0d2ccafaa99b9198dc5c7bbc7\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6775c4e0d2ccafaa99b9198dc5c7bbc7\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3c414797dbc39db6a242631605ef8e6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3c414797dbc39db6a242631605ef8e6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\752d364363cfe83d07ebd08f1853b21e\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\752d364363cfe83d07ebd08f1853b21e\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3929c15ecd3ed3bcbd327761aaa90f30\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3929c15ecd3ed3bcbd327761aaa90f30\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be6552077492e037cb648ba9d20c50b2\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be6552077492e037cb648ba9d20c50b2\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f4bdb46ff28ab587a34c53d6687bf99\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f4bdb46ff28ab587a34c53d6687bf99\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e927d56ef561df1431f9c8adb12ddc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e927d56ef561df1431f9c8adb12ddc\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92121f0061d84fd6603428dd9555221c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92121f0061d84fd6603428dd9555221c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.50] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8dbd27110790d4450617052df3c5d93\transformed\hilt-android-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.50] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8dbd27110790d4450617052df3c5d93\transformed\hilt-android-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42d76bf5c058c9faeb6229240c83266\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42d76bf5c058c9faeb6229240c83266\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80574656206597bee036fe941ba428c7\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80574656206597bee036fe941ba428c7\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd8b03a9aacee39a4f36b236dc8c1b64\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd8b03a9aacee39a4f36b236dc8c1b64\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af85d7b532fa32807b254d6290ddfca0\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af85d7b532fa32807b254d6290ddfca0\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268dd332085b36047c7c8b393feff7e2\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268dd332085b36047c7c8b393feff7e2\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cf6da45ccd670ee21b434999efbd41\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cf6da45ccd670ee21b434999efbd41\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea0d4e8623464af29dca49a272472225\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea0d4e8623464af29dca49a272472225\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d303f1f0d1dfdf89d203456ad79266\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d303f1f0d1dfdf89d203456ad79266\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78bf313ee09e26c8c671a0bbc2457ec1\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78bf313ee09e26c8c671a0bbc2457ec1\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1916aa273d557d541d17bc9f12ca7920\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1916aa273d557d541d17bc9f12ca7920\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4caf9741b7f09b99a2a3a0bd3b3d7c6b\transformed\media3-datasource-okhttp-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4caf9741b7f09b99a2a3a0bd3b3d7c6b\transformed\media3-datasource-okhttp-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb7207166b230fc420f90518c02881cc\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb7207166b230fc420f90518c02881cc\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195bf0261f5accce3675bff17e890464\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195bf0261f5accce3675bff17e890464\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c21fd9d5c777f873383f9d1179d8ce8\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c21fd9d5c777f873383f9d1179d8ce8\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b148c17468b063643693b5fb41c2c086\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b148c17468b063643693b5fb41c2c086\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a26edc275529aa4f7cf05aefcad03d8e\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a26edc275529aa4f7cf05aefcad03d8e\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b92fa3efd2e831e33070469b4d31f135\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b92fa3efd2e831e33070469b4d31f135\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040370f8816f4299de3bc74ca2c211f0\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\040370f8816f4299de3bc74ca2c211f0\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af754651ec5d146d4692fd33a60cd6b5\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af754651ec5d146d4692fd33a60cd6b5\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a822c4c17d161f53845a19fb128f54e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a822c4c17d161f53845a19fb128f54e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5877e86b535d8beee8141860616f120d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5877e86b535d8beee8141860616f120d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ecf52e2c51c09fa887ff15e8044d4a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ecf52e2c51c09fa887ff15e8044d4a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b127eabf3fe07a7bca176f62ec5e72c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b127eabf3fe07a7bca176f62ec5e72c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bf41a125552eeedf1dc74cafa954fee\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bf41a125552eeedf1dc74cafa954fee\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424b875497a500c66d36b320a185aeee\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424b875497a500c66d36b320a185aeee\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc26e2c2b777fa3787802ee43f5584c7\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc26e2c2b777fa3787802ee43f5584c7\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\422bad9bd1fca6b358e7b95759e82cab\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\422bad9bd1fca6b358e7b95759e82cab\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424fca0debd1544e67e0c0822c21d9e7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424fca0debd1544e67e0c0822c21d9e7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c6550fdb6c628b24ce9db4a741be8\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c6550fdb6c628b24ce9db4a741be8\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\518fe953358109ce0c8063a6625d5c4f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\518fe953358109ce0c8063a6625d5c4f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8b2f4de0ad0aa176dba9826c660dea7\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8b2f4de0ad0aa176dba9826c660dea7\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55d4dce1a1d094ee0ef77033f8ed8372\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55d4dce1a1d094ee0ef77033f8ed8372\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a46b3cd1f3124b42b4d032df4219956e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a46b3cd1f3124b42b4d032df4219956e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a33d50cf3518ee712f7b48e2658100\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a33d50cf3518ee712f7b48e2658100\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a572985955c753a461ab551d4cdef229\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a572985955c753a461ab551d4cdef229\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b859aed4ff87e5f7f2564514b13edb6\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b859aed4ff87e5f7f2564514b13edb6\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25a5afb01185fcabb33098703e48850c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25a5afb01185fcabb33098703e48850c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\452a3d61bd5a4df9c7b891c073bc51a8\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\452a3d61bd5a4df9c7b891c073bc51a8\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d18e326a7c95950bec51507cc408934\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d18e326a7c95950bec51507cc408934\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f63136d98328a54f1dd0cb208b70f66f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f63136d98328a54f1dd0cb208b70f66f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30a67fdc3d1b95d3c56f9db8b33f6a04\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30a67fdc3d1b95d3c56f9db8b33f6a04\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e79bbf8a7b26e1175d35315dd720102f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e79bbf8a7b26e1175d35315dd720102f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b36e363eec877c898c6a6b798c8febc9\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b36e363eec877c898c6a6b798c8febc9\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f62e4a4d8a407c6b06d9e47fff0e003\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f62e4a4d8a407c6b06d9e47fff0e003\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e490ace83d3e445f58e57bcd5ec1a3\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e490ace83d3e445f58e57bcd5ec1a3\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe9baeb1697de8666b4cdbdc3b7b7ec\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe9baeb1697de8666b4cdbdc3b7b7ec\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c6aba8d898bf5afa2792c4cbcbb6b8\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c6aba8d898bf5afa2792c4cbcbb6b8\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a53f08fca904f0165cea08f11ecb38\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a53f08fca904f0165cea08f11ecb38\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9eef6ad673a264cc89263a38493d29db\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9eef6ad673a264cc89263a38493d29db\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794c77caa217dc49438dbea4b18de558\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794c77caa217dc49438dbea4b18de558\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185d612fe844270dbe94dbecbf717d3b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185d612fe844270dbe94dbecbf717d3b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2b4430280827f7182af831eee6980fb\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2b4430280827f7182af831eee6980fb\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667cc5877b26e9c23bb3ff4b31c492\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667cc5877b26e9c23bb3ff4b31c492\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3fffdf45786e3c2b6c69e86201b2a80\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3fffdf45786e3c2b6c69e86201b2a80\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e0bcd8e0ccc9c7bfaf57677d7c3ed9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e0bcd8e0ccc9c7bfaf57677d7c3ed9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62ea7150938e602811be82533a7e3ed\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62ea7150938e602811be82533a7e3ed\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\661458eafcd301f339addf1cd11d3f67\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\661458eafcd301f339addf1cd11d3f67\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ce6023b12293a411384d5c2c100e77\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ce6023b12293a411384d5c2c100e77\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40b9f263e73af9f18fde6d0035bd27ff\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40b9f263e73af9f18fde6d0035bd27ff\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f71c86c6eabe71e98ff6abdb70ecc14\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f71c86c6eabe71e98ff6abdb70ecc14\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c376439633ee4e11e579bd0dba6d6e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c376439633ee4e11e579bd0dba6d6e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e0af7e6772353ecb8c2a0e492de2ce7\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e0af7e6772353ecb8c2a0e492de2ce7\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.50] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff1bdd17c51c51bdc66035dbacd7b9c\transformed\dagger-lint-aar-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.50] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff1bdd17c51c51bdc66035dbacd7b9c\transformed\dagger-lint-aar-2.50\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
uses-permission#android.permission.CAMERA
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:22-62
uses-feature#android.hardware.camera
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:27:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:26:9-47
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:35:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:34:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:48:13-74
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:10:9-12:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:12:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:11:13-84
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da80b951f206e73c375eb3bc3c9d40a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
