package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Album
import com.example.aimusicplayer.data.model.Banner
import com.example.aimusicplayer.data.model.PlayList
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 音乐探索的ViewModel
 * 负责处理音乐探索的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class DiscoveryViewModel @Inject constructor(
    application: Application,
    private val musicRepository: MusicRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "DiscoveryViewModel"
    }

    // Banner列表的StateFlow
    private val _bannerListFlow = MutableStateFlow<List<Banner>>(emptyList())
    val bannerListFlow: StateFlow<List<Banner>> = _bannerListFlow.asStateFlow()
    val bannerList: LiveData<List<Banner>> = bannerListFlow.asLiveData() // 兼容LiveData

    // 推荐歌单列表的StateFlow
    private val _recommendPlaylistListFlow = MutableStateFlow<List<PlayList>>(emptyList())
    val recommendPlaylistListFlow: StateFlow<List<PlayList>> = _recommendPlaylistListFlow.asStateFlow()
    val recommendPlaylistList: LiveData<List<PlayList>> = recommendPlaylistListFlow.asLiveData() // 兼容LiveData

    // 排行榜列表的StateFlow
    private val _toplistListFlow = MutableStateFlow<List<PlayList>>(emptyList())
    val toplistListFlow: StateFlow<List<PlayList>> = _toplistListFlow.asStateFlow()
    val toplistList: LiveData<List<PlayList>> = toplistListFlow.asLiveData() // 兼容LiveData

    // 新歌列表的StateFlow
    private val _newSongListFlow = MutableStateFlow<List<Song>>(emptyList())
    val newSongListFlow: StateFlow<List<Song>> = _newSongListFlow.asStateFlow()
    val newSongList: LiveData<List<Song>> = newSongListFlow.asLiveData() // 兼容LiveData

    // 新碟列表的StateFlow
    private val _newAlbumListFlow = MutableStateFlow<List<Album>>(emptyList())
    val newAlbumListFlow: StateFlow<List<Album>> = _newAlbumListFlow.asStateFlow()
    val newAlbumList: LiveData<List<Album>> = newAlbumListFlow.asLiveData() // 兼容LiveData

    init {
        // 初始化时加载数据
        loadAllData()
    }

    /**
     * 加载所有数据
     */
    fun loadAllData() {
        loadBanners()
        loadRecommendPlaylists()
        loadToplists()
        loadNewSongs()
        loadNewAlbums()
    }

    /**
     * 加载Banner
     */
    fun loadBanners() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载Banner失败", e)
                handleError(e, "加载Banner失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载Banner
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getBanners()
                }

                when (result) {
                    is NetworkResult.Success -> {
                        _bannerListFlow.value = result.data
                        Unit
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(result.message), result.message)
                        Unit
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                        Unit
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载推荐歌单
     */
    fun loadRecommendPlaylists() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载推荐歌单失败", e)
                handleError(e, "加载推荐歌单失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载推荐歌单
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getRecommendPlaylists()
                }

                when (result) {
                    is NetworkResult.Success -> {
                        _recommendPlaylistListFlow.value = result.data
                        Unit
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(result.message), result.message)
                        Unit
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                        Unit
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载排行榜
     */
    fun loadToplists() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载排行榜失败", e)
                handleError(e, "加载排行榜失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载排行榜
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getToplists()
                }

                when (result) {
                    is NetworkResult.Success -> {
                        _toplistListFlow.value = result.data
                        Unit
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(result.message), result.message)
                        Unit
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                        Unit
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载新歌
     */
    fun loadNewSongs() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载新歌失败", e)
                handleError(e, "加载新歌失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载新歌
                withContext(Dispatchers.IO) {
                    musicRepository.getNewSongsFlow().collect { result ->
                        when (result) {
                            is NetworkResult.Success -> {
                                _newSongListFlow.value = result.data
                            }
                            is NetworkResult.Error -> {
                                handleError(Exception(result.message), result.message)
                            }
                            is NetworkResult.Loading -> {
                                // 加载中状态处理
                            }
                        }
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载新碟
     */
    fun loadNewAlbums() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载新碟失败", e)
                handleError(e, "加载新碟失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载新碟
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getNewAlbums()
                }

                when (result) {
                    is NetworkResult.Success -> {
                        _newAlbumListFlow.value = result.data
                        Unit
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(result.message), result.message)
                        Unit
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                        Unit
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }
}
