package com.example.aimusicplayer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
// 移除过时的RenderScript导入，使用现代图像处理库
// import android.renderscript.Allocation
// import android.renderscript.Element
// import android.renderscript.RenderScript
// import android.renderscript.ScriptIntrinsicBlur
import jp.wasabeef.glide.transformations.BlurTransformation
import android.util.Log
import android.util.LruCache
import android.widget.ImageView
import androidx.palette.graphics.Palette
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.BitmapTransitionOptions
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.bumptech.glide.signature.ObjectKey
import com.example.aimusicplayer.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

/**
 * 图片工具类
 * 提供图片处理和缓存功能
 * 合并了ImageLoader和ImageUtils的功能
 */
object ImageUtils {
    private const val TAG = "ImageUtils"

    // 内存缓存大小（单位：KB）
    private const val MEMORY_CACHE_SIZE = 1024 * 10 // 10MB

    // 颜色提取缓存过期时间（毫秒）
    private const val COLOR_CACHE_EXPIRATION = 30 * 60 * 1000L // 30分钟

    // 最大颜色缓存条目数
    private const val MAX_COLOR_CACHE_SIZE = 100

    // 内存缓存
    private val bitmapCache = LruCache<String, Bitmap>(MEMORY_CACHE_SIZE)
    private val blurredBitmapCache = LruCache<String, Bitmap>(MEMORY_CACHE_SIZE / 2)
    private val colorCache = ConcurrentHashMap<String, ColorCacheEntry>()

    /**
     * 颜色类型枚举
     */
    enum class ColorType {
        DOMINANT,       // 主色调
        VIBRANT,        // 鲜艳的颜色
        DARK_VIBRANT,   // 深鲜艳的颜色
        LIGHT_VIBRANT,  // 浅鲜艳的颜色
        MUTED,          // 柔和的颜色
        DARK_MUTED,     // 深柔和的颜色
        LIGHT_MUTED     // 浅柔和的颜色
    }

    /**
     * 调整图片大小
     * @param bitmap 原始图片
     * @param width 目标宽度
     * @param height 目标高度
     * @return 调整大小后的图片，如果原始图片为null则返回null
     */
    fun resizeImage(bitmap: Bitmap?, width: Int, height: Int): Bitmap? {
        if (bitmap == null) {
            Log.w(TAG, "resizeImage: 输入的bitmap为null")
            return null
        }

        if (width <= 0 || height <= 0) {
            Log.w(TAG, "resizeImage: 无效的尺寸参数 width=$width, height=$height")
            return bitmap
        }

        val widthFloat = width.toFloat()
        val heightFloat = height.toFloat()

        val sourceWidth = bitmap.width.toFloat()
        val sourceHeight = bitmap.height.toFloat()

        // 如果尺寸相同，直接返回原图
        if (sourceWidth == widthFloat && sourceHeight == heightFloat) {
            return bitmap
        }

        val matrix = Matrix()
        val scaleWidth = widthFloat / sourceWidth
        val scaleHeight = heightFloat / sourceHeight
        matrix.postScale(scaleWidth, scaleHeight)

        return try {
            Bitmap.createBitmap(bitmap, 0, 0, sourceWidth.toInt(), sourceHeight.toInt(), matrix, true)
        } catch (e: Exception) {
            Log.e(TAG, "resizeImage: 调整图片大小失败", e)
            bitmap // 返回原图
        }
    }

    /**
     * 创建圆形图片
     * @param bitmap 原始图片
     * @return 圆形图片
     */
    fun createCircleBitmap(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val size = Math.min(width, height)
        val output = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val paint = Paint()

        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        canvas.drawCircle(size / 2f, size / 2f, size / 2f, paint)
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        val left = (size - width) / 2
        val top = (size - height) / 2
        val srcRect = Rect(0, 0, width, height)
        val destRect = Rect(left, top, left + width, top + height)
        canvas.drawBitmap(bitmap, srcRect, destRect, paint)

        return output
    }

    /**
     * 从Drawable创建Bitmap
     * @param drawable Drawable对象
     * @return Bitmap对象
     */
    fun drawableToBitmap(drawable: Drawable): Bitmap {
        if (drawable is BitmapDrawable) {
            return drawable.bitmap
        }

        val width = drawable.intrinsicWidth.takeIf { it > 0 } ?: 1
        val height = drawable.intrinsicHeight.takeIf { it > 0 } ?: 1
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)

        return bitmap
    }

    /**
     * 创建模糊图片 - 使用现代图像处理方法替代RenderScript
     * @param bitmap 原始图片
     * @param radius 模糊半径 (1-25) - 暂未使用，保留以备将来扩展
     * @return 模糊处理后的图片
     */
    fun createBlurredBitmap(@Suppress("UNUSED_PARAMETER") context: Context, bitmap: Bitmap, @Suppress("UNUSED_PARAMETER") radius: Float): Bitmap {
        return try {
            // 使用Canvas和Paint实现简单的模糊效果
            // 这是一个轻量级的替代方案，避免使用过时的RenderScript
            val scaledBitmap = Bitmap.createScaledBitmap(
                bitmap,
                bitmap.width / 4,
                bitmap.height / 4,
                false
            )

            val outputBitmap = Bitmap.createScaledBitmap(
                scaledBitmap,
                bitmap.width,
                bitmap.height,
                true
            )

            scaledBitmap.recycle()
            outputBitmap
        } catch (e: Exception) {
            Log.e(TAG, "创建模糊图片失败，返回原图", e)
            bitmap.copy(bitmap.config, false)
        }
    }

    /**
     * 模糊处理Bitmap
     * 与createBlurredBitmap功能相同，保留此方法是为了兼容性
     * @param context 上下文
     * @param bitmap 原始Bitmap
     * @param radius 模糊半径
     * @return 模糊后的Bitmap
     */
    fun blurBitmap(context: Context, bitmap: Bitmap, radius: Int): Bitmap {
        return createBlurredBitmap(context, bitmap, radius.toFloat())
    }

    /**
     * 使用Glide变换创建高质量模糊图片
     * @param context 上下文
     * @param imageView 目标ImageView
     * @param url 图片URL
     * @param radius 模糊半径 (1-25)
     * @param sampling 采样率 (1-8)
     */
    fun loadBlurredImage(
        context: Context,
        imageView: ImageView,
        url: Any?,
        radius: Int = 15,
        sampling: Int = 2
    ) {
        Glide.with(context)
            .load(url)
            .transform(BlurTransformation(radius, sampling))
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .into(imageView)
    }

    /**
     * 从图片中提取主色调
     * @param bitmap 图片
     * @param defaultColor 默认颜色
     * @return 主色调
     */
    suspend fun extractDominantColor(bitmap: Bitmap, defaultColor: Int): Int = withContext(Dispatchers.Default) {
        try {
            val palette = Palette.from(bitmap).generate()
            palette.getDominantColor(defaultColor)
        } catch (e: Exception) {
            Log.e(TAG, "提取主色调失败", e)
            defaultColor
        }
    }

    /**
     * 从图片中提取主色调（带缓存）
     * @param context 上下文
     * @param uri 图片URI
     * @param defaultColor 默认颜色
     * @return 主色调
     */
    suspend fun extractDominantColorWithCache(context: Context, uri: Uri, defaultColor: Int): Int = withContext(Dispatchers.IO) {
        val cacheKey = "color_${uri}"

        // 先从缓存获取
        colorCache.get(cacheKey)?.dominant?.let { return@withContext it }

        try {
            // 加载图片
            val bitmap = loadBitmapFromUri(context, uri) ?: return@withContext defaultColor

            // 提取颜色
            val color = extractDominantColor(bitmap, defaultColor)

            // 缓存颜色
            colorCache.put(cacheKey, ColorCacheEntry(
                dominant = color,
                vibrant = color,
                darkVibrant = color,
                lightVibrant = color,
                muted = color,
                darkMuted = color,
                lightMuted = color,
                timestamp = System.currentTimeMillis()
            ))

            color
        } catch (e: Exception) {
            Log.e(TAG, "提取主色调失败", e)
            defaultColor
        }
    }

    /**
     * 从URI加载Bitmap
     * @param context 上下文
     * @param uri 图片URI
     * @return Bitmap对象
     */
    suspend fun loadBitmapFromUri(context: Context, uri: Uri): Bitmap? = withContext(Dispatchers.IO) {
        try {
            // 添加超时和错误处理
            val future = Glide.with(context)
                .asBitmap()
                .load(uri)
                .apply(RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .timeout(5000) // 5秒超时
                    .centerCrop())
                .submit()

            // 使用超时获取结果
            future.get(3000, TimeUnit.MILLISECONDS)
        } catch (e: TimeoutException) {
            Log.e(TAG, "加载图片超时: $uri", e)
            // 返回默认图片
            loadDefaultAlbumArt(context)
        } catch (e: Exception) {
            Log.e(TAG, "加载图片失败: $uri", e)
            // 返回默认图片
            loadDefaultAlbumArt(context)
        }
    }

    /**
     * 加载默认专辑封面
     */
    private fun loadDefaultAlbumArt(context: Context): Bitmap? {
        return try {
            BitmapFactory.decodeResource(context.resources, R.drawable.default_album_art)
        } catch (e: Exception) {
            Log.e(TAG, "加载默认专辑封面失败", e)
            null
        }
    }

    /**
     * 加载并处理专辑封面
     * @param context 上下文
     * @param uri 图片URI
     * @param cacheKey 缓存键
     * @return 处理后的专辑封面
     */
    suspend fun loadAndProcessAlbumCover(context: Context, uri: Uri, cacheKey: String): Bitmap? = withContext(Dispatchers.IO) {
        // 先从缓存获取
        getCachedBitmap(cacheKey)?.let { return@withContext it }

        try {
            // 加载原始图片
            val originalBitmap = loadBitmapFromUri(context, uri) ?: return@withContext null

            // 创建圆形图片
            val circleBitmap = createCircleBitmap(originalBitmap)

            // 缓存处理后的图片
            cacheBitmap(cacheKey, circleBitmap)

            circleBitmap
        } catch (e: Exception) {
            Log.e(TAG, "处理专辑封面失败", e)
            null
        }
    }

    /**
     * 加载并创建模糊背景
     * @param context 上下文
     * @param uri 图片URI
     * @param cacheKey 缓存键
     * @param radius 模糊半径
     * @return 模糊处理后的背景图片
     */
    suspend fun loadAndCreateBlurredBackground(context: Context, uri: Uri, cacheKey: String, radius: Float): Bitmap? = withContext(Dispatchers.IO) {
        // 先从缓存获取
        getCachedBlurredBitmap(cacheKey)?.let { return@withContext it }

        try {
            // 加载原始图片
            val originalBitmap = loadBitmapFromUri(context, uri) ?: return@withContext null

            // 创建模糊图片
            val blurredBitmap = createBlurredBitmap(context, originalBitmap, radius)

            // 缓存模糊图片
            cacheBlurredBitmap(cacheKey, blurredBitmap)

            blurredBitmap
        } catch (e: Exception) {
            Log.e(TAG, "创建模糊背景失败", e)
            null
        }
    }

    /**
     * 缓存Bitmap
     * @param key 缓存键
     * @param bitmap 图片
     */
    fun cacheBitmap(key: String, bitmap: Bitmap) {
        bitmapCache.put(key, bitmap)
    }

    /**
     * 获取缓存的Bitmap
     * @param key 缓存键
     * @return 缓存的图片
     */
    fun getCachedBitmap(key: String): Bitmap? {
        return bitmapCache.get(key)
    }

    /**
     * 缓存模糊Bitmap
     * @param key 缓存键
     * @param bitmap 模糊图片
     */
    fun cacheBlurredBitmap(key: String, bitmap: Bitmap) {
        blurredBitmapCache.put(key, bitmap)
    }

    /**
     * 获取缓存的模糊Bitmap
     * @param key 缓存键
     * @return 缓存的模糊图片
     */
    fun getCachedBlurredBitmap(key: String): Bitmap? {
        return blurredBitmapCache.get(key)
    }

    /**
     * 清除所有缓存
     */
    fun clearAllCache() {
        bitmapCache.evictAll()
        blurredBitmapCache.evictAll()
        colorCache.clear()
    }

    /**
     * 保存Bitmap到文件
     * @param context 上下文
     * @param bitmap 图片
     * @param fileName 文件名
     * @return 保存的文件
     */
    suspend fun saveBitmapToFile(context: Context, bitmap: Bitmap, fileName: String): File? = withContext(Dispatchers.IO) {
        val file = File(context.cacheDir, fileName)
        var outputStream: FileOutputStream? = null

        try {
            outputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
            outputStream.flush()
            file
        } catch (e: IOException) {
            Log.e(TAG, "保存图片到文件失败", e)
            null
        } finally {
            try {
                outputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "关闭输出流失败", e)
            }
        }
    }

    /**
     * 加载图片到ImageView
     * 优化版本：支持更多参数和回调
     *
     * @param context 上下文
     * @param url 图片URL或资源ID
     * @param imageView 目标ImageView
     * @param placeholder 占位图资源ID
     * @param error 错误图资源ID
     * @param isCircle 是否裁剪为圆形
     * @param cornerRadius 圆角半径，仅在isCircle为false时有效
     * @param crossFade 是否使用淡入淡出效果
     * @param diskCacheStrategy 磁盘缓存策略
     * @param skipMemoryCache 是否跳过内存缓存
     * @param signature 缓存签名，用于强制刷新缓存
     * @param listener 加载监听器
     */
    fun load(
        context: Context,
        url: Any?,
        imageView: ImageView,
        placeholder: Int = R.drawable.default_album_art,
        error: Int = R.drawable.default_album_art,
        isCircle: Boolean = false,
        cornerRadius: Int = 0,
        crossFade: Boolean = true,
        diskCacheStrategy: DiskCacheStrategy = DiskCacheStrategy.AUTOMATIC,
        skipMemoryCache: Boolean = false,
        signature: Any? = null,
        listener: ImageLoadListener? = null
    ) {
        if (url == null) {
            if (error != 0) {
                imageView.setImageResource(error)
            }
            listener?.onFailed()
            return
        }

        // 创建请求选项
        val options = RequestOptions()
            .diskCacheStrategy(diskCacheStrategy)
            .skipMemoryCache(skipMemoryCache)

        // 设置占位图
        if (placeholder != 0) {
            options.placeholder(placeholder)
        }

        // 设置错误图
        if (error != 0) {
            options.error(error)
        }

        // 设置变换
        if (isCircle) {
            options.transform(CircleCrop())
        } else if (cornerRadius > 0) {
            options.transform(RoundedCorners(cornerRadius))
        }

        // 设置缓存签名
        if (signature != null) {
            options.signature(ObjectKey(signature))
        }

        // 创建请求
        val request = when (url) {
            is String -> {
                if (url.startsWith("http")) {
                    // 网络图片
                    Glide.with(context).load(url)
                } else if (url.startsWith("file://")) {
                    // 本地文件
                    Glide.with(context).load(File(url.substring(7)))
                } else {
                    // 其他字符串，可能是资源名称
                    val resourceId = context.resources.getIdentifier(
                        url, "drawable", context.packageName
                    )
                    if (resourceId != 0) {
                        Glide.with(context).load(resourceId)
                    } else {
                        Glide.with(context).load(url)
                    }
                }
            }
            is Int -> {
                // 资源ID
                Glide.with(context).load(url)
            }
            is File -> {
                // 文件对象
                Glide.with(context).load(url)
            }
            is Bitmap -> {
                // Bitmap对象
                Glide.with(context).load(url)
            }
            is Drawable -> {
                // Drawable对象
                Glide.with(context).load(url)
            }
            is Uri -> {
                // Uri对象
                Glide.with(context).load(url)
            }
            else -> {
                // 其他类型
                Glide.with(context).load(url.toString())
            }
        }

        // 应用选项
        request.apply(options)

        // 设置过渡动画
        if (crossFade) {
            if (url is Bitmap) {
                @Suppress("UNCHECKED_CAST")
                (request as RequestBuilder<Bitmap>).transition(BitmapTransitionOptions.withCrossFade())
            } else {
                @Suppress("UNCHECKED_CAST")
                (request as RequestBuilder<Drawable>).transition(DrawableTransitionOptions.withCrossFade())
            }
        }

        // 设置监听器
        if (listener != null) {
            // 根据请求类型设置不同的监听器
            if (url is Bitmap) {
                @Suppress("UNCHECKED_CAST")
                (request as RequestBuilder<Bitmap>).listener(object : RequestListener<Bitmap> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Bitmap?>, // Changed to Target<Bitmap?>
                        isFirstResource: Boolean
                    ): Boolean {
                        listener.onFailed()
                        return false
                    }

                    override fun onResourceReady(
                        resource: Bitmap,
                        model: Any, // Changed to Any
                        target: Target<Bitmap?>, // Changed to Target<Bitmap?>
                        dataSource: DataSource, // Changed to DataSource
                        isFirstResource: Boolean
                    ): Boolean {
                        listener.onSuccess()
                        return false
                    }
                })
            } else {
                @Suppress("UNCHECKED_CAST")
                (request as RequestBuilder<Drawable>).listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable?>, // Changed to Target<Drawable?>
                        isFirstResource: Boolean
                    ): Boolean {
                        listener.onFailed()
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any, // Changed to Any
                        target: Target<Drawable?>, // Changed to Target<Drawable?>
                        dataSource: DataSource, // Changed to DataSource
                        isFirstResource: Boolean
                    ): Boolean {
                        listener.onSuccess()
                        return false
                    }
                })
            }
        }

        // 加载图片
        request.into(imageView)
    }

    /**
     * 加载图片（兼容旧版本）
     * @param context 上下文
     * @param url 图片URL
     * @param imageView 目标ImageView
     * @param placeholder 占位图资源ID
     * @param error 错误图资源ID
     */
    @JvmStatic
    fun loadImage(
        context: Context,
        url: String?,
        imageView: ImageView,
        placeholder: Int,
        error: Int
    ) {
        load(
            context = context,
            url = url,
            imageView = imageView,
            placeholder = placeholder,
            error = error
        )
    }

    /**
     * 预加载图片
     * @param context 上下文
     * @param url 图片URL
     */
    fun preload(context: Context, url: Any?) {
        Glide.with(context)
            .load(url)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .preload()
    }

    /**
     * 加载图片并提取主色调
     * 优化版本：支持更好的缓存策略和多种颜色提取方式
     *
     * @param context 上下文
     * @param url 图片URL
     * @param colorType 颜色提取类型，默认为主色调
     * @param defaultColor 默认颜色，当提取失败时返回
     * @param cacheKey 自定义缓存键，为null时使用url的字符串表示
     * @param forceExtract 是否强制重新提取，忽略缓存
     * @param callback 颜色提取完成回调
     */
    fun loadAndExtractColor(
        context: Context,
        url: Any?,
        colorType: ColorType = ColorType.DOMINANT,
        defaultColor: Int = 0xFF333333.toInt(),
        cacheKey: String? = null,
        forceExtract: Boolean = false,
        callback: (Int) -> Unit
    ) {
        // 如果URL为空，直接返回默认颜色
        if (url == null) {
            callback(defaultColor)
            return
        }

        // 生成缓存键
        val key = cacheKey ?: url.toString()

        // 如果不强制提取，尝试从缓存获取颜色
        if (!forceExtract) {
            val cachedEntry = colorCache[key]
            if (cachedEntry != null) {
                // 检查缓存是否过期
                if (System.currentTimeMillis() - cachedEntry.timestamp < COLOR_CACHE_EXPIRATION) {
                    // 缓存有效，直接返回缓存的颜色
                    callback(cachedEntry.getColor(colorType, defaultColor))
                    return
                } else {
                    // 缓存过期，移除
                    colorCache.remove(key)
                }
            }
        }

        // 清理缓存，如果缓存条目数超过最大值
        if (colorCache.size > MAX_COLOR_CACHE_SIZE) {
            cleanColorCache()
        }

        // 加载图片并提取颜色
        Glide.with(context)
            .asBitmap()
            .load(url)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 使用Palette提取颜色
                    Palette.from(resource).generate { palette ->
                        if (palette == null) {
                            callback(defaultColor)
                            return@generate
                        }

                        // 提取各种颜色
                        val dominant = palette.getDominantColor(defaultColor)
                        val vibrant = palette.getVibrantColor(dominant)
                        val darkVibrant = palette.getDarkVibrantColor(dominant)
                        val lightVibrant = palette.getLightVibrantColor(dominant)
                        val muted = palette.getMutedColor(dominant)
                        val darkMuted = palette.getDarkMutedColor(dominant)
                        val lightMuted = palette.getLightMutedColor(dominant)

                        // 创建缓存条目
                        val entry = ColorCacheEntry(
                            dominant = dominant,
                            vibrant = vibrant,
                            darkVibrant = darkVibrant,
                            lightVibrant = lightVibrant,
                            muted = muted,
                            darkMuted = darkMuted,
                            lightMuted = lightMuted,
                            timestamp = System.currentTimeMillis()
                        )

                        // 缓存颜色
                        colorCache[key] = entry

                        // 返回请求的颜色类型
                        callback(entry.getColor(colorType, defaultColor))
                    }
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    callback(defaultColor)
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    callback(defaultColor)
                }
            })
    }

    /**
     * 清理颜色缓存
     * 移除最旧的条目，直到缓存大小减半
     */
    private fun cleanColorCache() {
        // 按时间戳排序
        val sortedEntries = colorCache.entries.sortedBy { it.value.timestamp }

        // 移除一半的条目
        val removeCount = colorCache.size / 2
        sortedEntries.take(removeCount).forEach { (key, @Suppress("UNUSED_VARIABLE") _) ->
            colorCache.remove(key)
        }
    }

    /**
     * 颜色缓存条目
     * 存储从图片中提取的各种颜色和时间戳
     */
    private data class ColorCacheEntry(
        val dominant: Int,
        val vibrant: Int,
        val darkVibrant: Int,
        val lightVibrant: Int,
        val muted: Int,
        val darkMuted: Int,
        val lightMuted: Int,
        val timestamp: Long
    ) {
        /**
         * 获取指定类型的颜色
         *
         * @param type 颜色类型
         * @param defaultColor 默认颜色（暂未使用，保留以备将来扩展）
         * @return 颜色值
         */
        fun getColor(type: ColorType, @Suppress("UNUSED_PARAMETER") defaultColor: Int): Int {
            return when (type) {
                ColorType.DOMINANT -> dominant
                ColorType.VIBRANT -> vibrant
                ColorType.DARK_VIBRANT -> darkVibrant
                ColorType.LIGHT_VIBRANT -> lightVibrant
                ColorType.MUTED -> muted
                ColorType.DARK_MUTED -> darkMuted
                ColorType.LIGHT_MUTED -> lightMuted
            }
        }
    }

    /**
     * 图片加载监听器
     * 优化版本：使用默认实现，简化使用
     */
    interface ImageLoadListener {
        fun onSuccess() {}
        fun onFailed() {}
    }
}
