package com.example.aimusicplayer.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.util.Log

/**
 * 网络工具类
 * 用于检查网络状态和处理网络请求结果
 */
object NetworkUtils {

    /**
     * 检查网络是否可用 - 使用现代API
     * @param context 上下文
     * @return 网络是否可用
     */
    @JvmStatic
    fun isNetworkAvailable(context: Context?): Boolean {
        if (context == null) {
            return false
        }

        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false

        return try {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

            // 检查网络是否有互联网连接能力且已验证
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } catch (e: Exception) {
            Log.e("NetworkUtils", "检查网络状态失败", e)
            false
        }
    }

    /**
     * 检查是否是WiFi连接 - 使用现代API
     * @param context 上下文
     * @return 是否是WiFi连接
     */
    @JvmStatic
    fun isWifiConnected(context: Context?): Boolean {
        if (context == null) {
            return false
        }

        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false

        return try {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } catch (e: Exception) {
            Log.e("NetworkUtils", "检查WiFi状态失败", e)
            false
        }
    }

    /**
     * 检查是否是移动网络连接 - 使用现代API
     * @param context 上下文
     * @return 是否是移动网络连接
     */
    @JvmStatic
    fun isMobileConnected(context: Context?): Boolean {
        if (context == null) {
            return false
        }

        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false

        return try {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        } catch (e: Exception) {
            Log.e("NetworkUtils", "检查移动网络状态失败", e)
            false
        }
    }

    /**
     * 获取网络类型
     * @param context 上下文
     * @return 网络类型字符串
     */
    @JvmStatic
    fun getNetworkType(context: Context?): String {
        if (context == null) return "UNKNOWN"

        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return "UNKNOWN"

        return try {
            val network = connectivityManager.activeNetwork ?: return "NONE"
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return "UNKNOWN"

            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WIFI"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "CELLULAR"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "ETHERNET"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "BLUETOOTH"
                else -> "OTHER"
            }
        } catch (e: Exception) {
            Log.e("NetworkUtils", "获取网络类型失败", e)
            "UNKNOWN"
        }
    }

    /**
     * 注册网络状态监听器
     * @param context 上下文
     * @param callback 网络状态变化回调
     */
    @JvmStatic
    fun registerNetworkCallback(context: Context, callback: ConnectivityManager.NetworkCallback) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return

        try {
            val networkRequest = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
            connectivityManager.registerNetworkCallback(networkRequest, callback)
        } catch (e: Exception) {
            Log.e("NetworkUtils", "注册网络监听器失败", e)
        }
    }

    /**
     * 取消注册网络状态监听器
     * @param context 上下文
     * @param callback 网络状态变化回调
     */
    @JvmStatic
    fun unregisterNetworkCallback(context: Context, callback: ConnectivityManager.NetworkCallback) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return

        try {
            connectivityManager.unregisterNetworkCallback(callback)
        } catch (e: Exception) {
            Log.e("NetworkUtils", "取消注册网络监听器失败", e)
        }
    }
}
