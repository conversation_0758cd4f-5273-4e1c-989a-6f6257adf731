                                                                                                    	at com.android.networkstack.tethering.Tethering.updateSupportedDownstreams(Tethering.java:2483)
                                                                                                    	at com.android.networkstack.tethering.Tethering.updateConfiguration(Tethering.java:504)
                                                                                                    	at com.android.networkstack.tethering.Tethering.lambda$new$3(Tethering.java:339)
                                                                                                    	at com.android.networkstack.tethering.Tethering.$r8$lambda$gUNv5kQ9F3l-55GJaHD0NFxn_Fo(Unknown Source:0)
                                                                                                    	at com.android.networkstack.tethering.Tethering$$ExternalSyntheticLambda4.accept(Unknown Source:4)
                                                                                                    	at com.android.networkstack.tethering.util.VersionedBroadcastListener$Receiver.onReceive(VersionedBroadcastListener.java:103)
                                                                                                    	at android.app.LoadedApk$ReceiverDispatcher$Args.lambda$getRunnable$0$android-app-LoadedApk$ReceiverDispatcher$Args(LoadedApk.java:1790)
                                                                                                    	at android.app.LoadedApk$ReceiverDispatcher$Args$$ExternalSyntheticLambda0.run(Unknown Source:2)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 16:53:29.518  2758-2758  ocalmediaplayer         com...id.car.media.localmediaplayer  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:29.616   616-653   GnssUtilsJni            system_server                        E  IGnssConfiguration setEsExtensionSec() failed.
2025-05-25 16:53:29.616   616-653   GnssConfiguration       system_server                        E  Unable to set ES_EXTENSION_SEC: 0
2025-05-25 16:53:29.723   616-616   ActivityThread          system_server                        E  Failed to find provider info for com.android.blockednumber
2025-05-25 16:53:29.790   236-236   tombstoned              tombstoned                           E  Tombstone written to: tombstone_10
2025-05-25 16:53:29.795   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:29.997   616-753   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 16:53:30.030  2796-2796  d.process.media         android.process.media                E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:30.092   616-937   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-25 16:53:30.132  2821-2821  viceentitlement         com.android.imsserviceentitlement    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:30.357   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:30.690  2559-2805  Finsky                  com.android.vending                  E  [176] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:30.873   616-787   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-25 16:53:31.002  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:31.009  1569-1969  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 16:53:31.038  2559-2815  Finsky                  com.android.vending                  E  [178] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-25 16:53:31.039  2559-2815  Finsky                  com.android.vending                  E  [178] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:31.228  2946-2946  android.vending         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:31.340  2963-2963  e.process.gapps         com.google.process.gapps             E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:31.532  2984-2984  ding:background         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:31.626  3007-3007  viceentitlement         com.android.imsserviceentitlement    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:32.130  2593-2640  CarSetupWizard          com.google.android.car.setupwizard   E  [clf] Timeout reached before completing retrieval for PersistentFlags Waited 100 milliseconds (plus 546600 nanoseconds delay) for feb@86ce4d1[status=PENDING, info=[delegate=[fcq@ebb4b36[status=PENDING, setFuture=[feb@86daa37[status=PENDING, info=[delegate=[ffh@e186ba4[status=PENDING, info=[task=[running=[RUNNING ON BG Thread #0], propagating=[dzu@609fd0d]]]]]]]]]]]]
2025-05-25 16:53:32.370  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085332.370330:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2c7e43b9
2025-05-25 16:53:32.370  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085332.370969:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2c7e43b9
2025-05-25 16:53:32.371  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085332.371027:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2c7e43b9
2025-05-25 16:53:32.371  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085332.371077:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2c7e43b9
2025-05-25 16:53:32.371  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085332.371126:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2c7e43b9
2025-05-25 16:53:32.371  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085332.371184:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2c7e43b9
2025-05-25 16:53:32.846  3102-3102  viders.calendar         com.android.providers.calendar       E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:33.017  1569-1589  UsageReportingOptionsSt com.google.android.gms.persistent    E  INTERNAL_ERROR: setOptInOption should not be called while user is locked. [CONTEXT service_id=41 ]
2025-05-25 16:53:33.215  3150-3150  id.gms.unstable         com.google.android.gms.unstable      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:33.368  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085333.368178:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2c7ec935
2025-05-25 16:53:33.368  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085333.368214:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2c7ec935
2025-05-25 16:53:33.368  2896-2896  droid.bluetooth         com.google.android.bluetooth         E  [0525/085333.368241:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-25 16:53:33.401  2946-3058  Finsky                  com.android.vending                  E  [206] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:34.106  2946-3060  Finsky                  com.android.vending                  E  [209] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-25 16:53:34.107  2946-3060  Finsky                  com.android.vending                  E  [209] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:34.262  2499-2819  tflite                  com.google.android.tts               E  third_party/tensorflow/lite/core/subgraph.cc:1059 tensor.data.raw != nullptr was not true.
2025-05-25 16:53:34.503   616-1178  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-25 16:53:34.520   616-1178  InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=6, name='1da4f96 com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=5, name='91f3612 com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='6c8d00 com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=2, name='ed1d38f com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-25 16:53:34.557  2281-2281  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-25 16:53:34.681  2281-2516  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-25 16:53:34.874  2559-2868  Finsky                  com.android.vending                  E  [197] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:34.875  2559-2868  Finsky                  com.android.vending                  E  [197] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:34.875  2559-2868  Finsky                  com.android.vending                  E  [197] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:34.880  2559-2815  Finsky                  com.android.vending                  E  [178] kzd.run(1284): Upload device configuration failed
2025-05-25 16:53:35.118  3280-3280  DEBUG                   crash_dump64                         E  failed to read process info: failed to open /proc/2896
2025-05-25 16:53:35.223  2559-2862  Finsky                  com.android.vending                  E  [193] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:35.346  2559-2862  Finsky                  com.android.vending                  E  [193] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-25 16:53:35.394  1776-2261  UsageReportingOptionsSt com.google.android.gms.persistent    E  INTERNAL_ERROR: setOptInOption should not be called while user is locked. [CONTEXT service_id=41 ]
2025-05-25 16:53:36.005  1569-1972  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 16:53:36.360   236-236   tombstoned              tombstoned                           E  Tombstone written to: tombstone_11
2025-05-25 16:53:36.361   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:36.362   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:36.664   616-753   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 16:53:36.696  2946-3173  Finsky                  com.android.vending                  E  [225] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:36.697  2946-3173  Finsky                  com.android.vending                  E  [225] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:36.697  2946-3173  Finsky                  com.android.vending                  E  [225] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:36.719  2946-3172  Finsky                  com.android.vending                  E  [224] kzd.run(1284): Upload device configuration failed
2025-05-25 16:53:36.971  3387-3387  ding:background         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:37.440  2946-3066  Finsky                  com.android.vending                  E  [211] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:53:37.444  3435-3435  or.multidisplay         com.android.emulator.multidisplay    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:37.567  2946-3066  Finsky                  com.android.vending                  E  [211] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-25 16:53:37.849  2281-2281  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-25 16:53:37.869  2281-2516  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-25 16:53:37.913  2281-2516  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-25 16:53:38.080  3435-3472  MultiDisplayService     com.android.emulator.multidisplay    E  invalid parameters for add/modify display
2025-05-25 16:53:38.483  3487-3487  id.gms.unstable         com.google.android.gms.unstable      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:38.600  1469-1798  native                  com.google.android.carassistant      E  E0000 00:00:**********.600554    1798 soda_client.cc:674] Cannot setup DataProvider due to missing instance.
2025-05-25 16:53:38.786  1469-1886  native                  com.google.android.carassistant      E  E0000 00:00:**********.604533    1886 soda_client.cc:674] Cannot setup DataProvider due to missing instance.
2025-05-25 16:53:39.118  2281-2281  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-25 16:53:39.124  3522-3522  moteprovisioner         com.android.remoteprovisioner        E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:39.305  1469-1885  native                  com.google.android.carassistant      E  E0000 00:00:**********.287118    1885 soda_client.cc:674] Cannot setup DataProvider due to missing instance.
2025-05-25 16:53:39.439  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:39.466  3594-3594  externalstorage         com.android.externalstorage          E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:39.561  1469-1798  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-25 16:53:39.566  1469-1798  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-25 16:53:40.178  2679-3225  Handwritin...ngStrategy com...d.apps.automotive.inputmethod  E  HandwritingSlicingStrategy.getSlices():85 getSlices(): packMapping unavailable for en-US
2025-05-25 16:53:40.262  2679-3666  Handwritin...rpacksUtil com...d.apps.automotive.inputmethod  E  HandwritingSuperpacksUtil.getPackMappingPackName():40 No pack mapping pack found in []
2025-05-25 16:53:40.354  1469-1885  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-25 16:53:40.357  1469-1886  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-25 16:53:40.362  1469-1885  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-25 16:53:40.364  1469-1886  SpeechMicro             com.google.android.carassistant      E  Hotword model is single-channel neuralnet.
2025-05-25 16:53:40.438   339-339   WVCdm                   and...hardware.drm-service.widevine  E  [oemcrypto_adapter_dynamic.cpp(958):Level1Terminate] L1 Terminate not needed
2025-05-25 16:53:40.440  2679-3154  Handwritin...rpacksUtil com...d.apps.automotive.inputmethod  E  HandwritingSuperpacksUtil.getPackMappingPackName():40 No pack mapping pack found in []
2025-05-25 16:53:40.453  3684-3684  d.process.media         android.process.media                E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:40.599  3716-3716  externalstorage         com.android.externalstorage          E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:40.924   616-753   BluetoothManagerService system_server                        E  MESSAGE_TIMEOUT_BIND
2025-05-25 16:53:41.194  3764-3764  m.android.shell         com.android.shell                    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:41.356  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085341.356374:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2a9e53b9
2025-05-25 16:53:41.356  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085341.356421:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2a9e53b9
2025-05-25 16:53:41.356  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085341.356448:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2a9e53b9
2025-05-25 16:53:41.356  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085341.356471:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2a9e53b9
2025-05-25 16:53:41.356  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085341.356494:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2a9e53b9
2025-05-25 16:53:41.356  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085341.356512:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2a9e53b9
2025-05-25 16:53:41.652  2317-2317  droid.apps.maps         com.google.android.apps.maps         E  No package ID ff found for ID 0xffffffff.
2025-05-25 16:53:41.871  3837-3837  tatementservice         com.android.statementservice         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:42.339  2317-3169  OpenGLRenderer          com.google.android.apps.maps         E  Unable to match the desired swap behavior.
2025-05-25 16:53:42.369  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085342.368862:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2a9ed935
2025-05-25 16:53:42.369  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085342.369105:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2a9ed935
2025-05-25 16:53:42.369  3584-3584  droid.bluetooth         com.google.android.bluetooth         E  [0525/085342.369161:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-25 16:53:42.521  2317-3169  OpenGLRenderer          com.google.android.apps.maps         E  Unable to match the desired swap behavior.
2025-05-25 16:53:42.874  1569-3081  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 16:53:43.576  3902-3902  DEBUG                   crash_dump64                         E  failed to read process info: failed to open /proc/3584
2025-05-25 16:53:43.663  3907-3907  d.configupdater         com.google.android.configupdater     E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:44.119   236-236   tombstoned              tombstoned                           E  Tombstone written to: tombstone_12
2025-05-25 16:53:44.120   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:44.123   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:44.267   616-753   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 16:53:44.340  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.355  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.455  3522-3544  DrmHalHidl              com.android.remoteprovisioner        E  Failed to get vendor from drm plugin: -19
2025-05-25 16:53:44.455  3522-3544  DrmHalHidl              com.android.remoteprovisioner        E  Failed to get description from drm plugin: -19
2025-05-25 16:53:44.547  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.584  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.589  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.611  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.640  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.649  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.659  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.690  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.708  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.714  3907-3907  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:53:44.948  3988-3988  ackageinstaller         com.google.android.packageinstaller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:45.624  2559-2851  Finsky                  com.android.vending                  E  [188] gjj.accept(91): GMS compliance query failed.
                                                                                                    com.google.common.util.concurrent.TimeoutFuture$TimeoutFutureException: Timed out: ablb@c8d9fb9[status=PENDING, setFuture=[dqd@10d8ffe[status=PENDING, info=[tag=[vxg@119e35f]]]]]
2025-05-25 16:53:45.840  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:45.872  1745-4009  PhenotypeRegOp          com.google.android.gms               E  Attempting to overwrite config package for com.google.android.gms.auth_account_auto#com.google.android.gms [CONTEXT service_id=231 ]
2025-05-25 16:53:46.121  1058-1058  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-25 16:53:46.224  1058-1058  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-25 16:53:46.251  4052-4052  ssioncontroller         com....android.permissioncontroller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:46.439  1058-1058  SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-25 16:53:46.453   429-448   installd                installd                             E  Couldn't opendir /data/app/vmdl631535505.tmp: No such file or directory
2025-05-25 16:53:46.453   429-448   installd                installd                             E  Failed to delete /data/app/vmdl631535505.tmp: No such file or directory
2025-05-25 16:53:46.508  1569-1722  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@fc4d7969, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:46.742  1569-4017  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 16:53:47.044  2130-2130  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_ADDED
2025-05-25 16:53:47.072  2130-2130  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_REPLACED
2025-05-25 16:53:47.354  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085347.343343:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b26be53b9
2025-05-25 16:53:47.354  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085347.354931:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b26be53b9
2025-05-25 16:53:47.355  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085347.354991:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b26be53b9
2025-05-25 16:53:47.355  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085347.355009:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b26be53b9
2025-05-25 16:53:47.358  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085347.355029:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b26be53b9
2025-05-25 16:53:47.358  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085347.358802:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b26be53b9
2025-05-25 16:53:47.567   616-2798  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-25 16:53:47.934  1776-2375  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@c4a7130f, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:48.035  2281-2516  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-25 16:53:48.252  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085348.252224:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b26bed935
2025-05-25 16:53:48.252  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085348.252294:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b26bed935
2025-05-25 16:53:48.252  4023-4023  droid.bluetooth         com.google.android.bluetooth         E  [0525/085348.252358:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-25 16:53:48.280  4166-4166  roid.car.dialer         com.android.car.dialer               E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:48.334  2946-3174  Finsky                  com.android.vending                  E  [226] gjj.accept(91): GMS compliance query failed.
                                                                                                    com.google.common.util.concurrent.TimeoutFuture$TimeoutFutureException: Timed out: ablb@9284c47[status=PENDING, setFuture=[dqd@3504c74[status=PENDING, info=[tag=[vxg@634159d]]]]]
2025-05-25 16:53:48.553  4196-4196  ssioncontroller         com....android.permissioncontroller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:49.269  2130-2510  native                  com.google.android.gms               E  E0000 00:00:1748163229.269697    2510 array-storage.cc:100] Array storage bad crc 3802234335 vs 257233327
2025-05-25 16:53:49.337  4221-4221  DEBUG                   crash_dump64                         E  failed to read process info: failed to open /proc/4023
2025-05-25 16:53:49.340  2130-2510  native                  com.google.android.gms               E  E0000 00:00:1748163229.340233    2510 dynamic-trie.cc:498] Trie mmap suffix failed
2025-05-25 16:53:49.628  4231-4231  d.car.messenger         com.android.car.messenger            E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:50.301   236-236   tombstoned              tombstoned                           E  Tombstone written to: tombstone_13
2025-05-25 16:53:50.386   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:50.387   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:50.437  2130-2510  native                  com.google.android.gms               E  E0000 00:00:1748163230.254696    2510 flash-index.cc:708] Lite index failed to initialize; clearing: DATA_LOSS: Lite index crc check failed: 2309419672 vs 2624163573
2025-05-25 16:53:50.571   616-753   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 16:53:50.804  4275-4275  ndroid.contacts         com.android.contacts                 E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:51.026  4137-4137  RenderingOptimizer      com.example.aimusicplayer            E  渲染模式优化失败，回退到传统方式 (Ask Gemini)
                                                                                                    java.lang.NullPointerException: Attempt to invoke virtual method 'android.view.WindowInsetsController com.android.internal.policy.DecorView.getWindowInsetsController()' on a null object reference
                                                                                                    	at com.android.internal.policy.PhoneWindow.getInsetsController(PhoneWindow.java:3959)
                                                                                                    	at com.example.aimusicplayer.utils.RenderingOptimizer.optimizeRenderingMode(RenderingOptimizer.kt:95)
                                                                                                    	at com.example.aimusicplayer.utils.RenderingOptimizer.optimizeActivityRendering(RenderingOptimizer.kt:41)
                                                                                                    	at com.example.aimusicplayer.ui.splash.SplashActivity.onCreate(SplashActivity.java:43)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:8342)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:8321)
                                                                                                    	at android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1417)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3626)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3782)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:101)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:138)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:95)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2307)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-25 16:53:51.167   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:51.167   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:51.167   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:51.222  4306-4306  droid.dynsystem         com.android.dynsystem                E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:51.482  1745-4009  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.mg.ui.main.MainActivity' is not available.
2025-05-25 16:53:51.483  1745-4009  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SettingsLoaderActivity' is not available.
2025-05-25 16:53:51.484  1745-4009  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.service.PurgeScreenDataService' is not available.
2025-05-25 16:53:51.486  1745-4009  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.ZeroPartyEntryPointActivity' is not available.
2025-05-25 16:53:51.512  1745-4321  adservices              com.google.android.gms               E  Exception caught when modifying AdExtDtaService state! [CONTEXT service_id=261 ] (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Component class com.google.android.gms.adsidentity.service.AdServicesExtDataStorageService does not exist in com.google.android.gms
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3015)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.content.pm.IPackageManager$Stub$Proxy.setComponentEnabledSetting(IPackageManager.java:5881)
                                                                                                    	at android.app.ApplicationPackageManager.setComponentEnabledSetting(ApplicationPackageManager.java:3050)
                                                                                                    	at adui.m(:com.google.android.gms@*********@24.26.32 (230800-*********):30)
                                                                                                    	at aduj.M(:com.google.android.gms@*********@24.26.32 (230800-*********):12)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.d(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.b(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at zyx.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):136)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):22)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at lnq.run(:com.google.android.gms@*********@24.26.32 (230800-*********):70)
                                                                                                    	at lnp.run(:com.google.android.gms@*********@24.26.32 (230800-*********):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:51.520  4339-4339  gedprovisioning         com.android.managedprovisioning      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:51.604  1745-4009  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.PrivacyHubActivityControlsActivity' is not available.
2025-05-25 16:53:51.605  1745-4009  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SafetyCenterActivityControlsActivity' is not available.
2025-05-25 16:53:51.950  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:51.959  1745-4322  JavaBinder              com.google.android.gms               E  !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
2025-05-25 16:53:52.250  1569-1717  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@f53f7bd4, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:53:52.680  4137-4280  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 16:53:53.089  4404-4404  m.android.shell         com.android.shell                    E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:53.156   616-1346  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-25 16:53:53.334   324-400   BpfHandler              netd                                 E  Unsupported protocol: 1
2025-05-25 16:53:53.461  4137-4137  RenderingOptimizer      com.example.aimusicplayer            E  渲染模式优化失败，回退到传统方式 (Ask Gemini)
                                                                                                    java.lang.NullPointerException: Attempt to invoke virtual method 'android.view.WindowInsetsController com.android.internal.policy.DecorView.getWindowInsetsController()' on a null object reference
                                                                                                    	at com.android.internal.policy.PhoneWindow.getInsetsController(PhoneWindow.java:3959)
                                                                                                    	at com.example.aimusicplayer.utils.RenderingOptimizer.optimizeRenderingMode(RenderingOptimizer.kt:95)
                                                                                                    	at com.example.aimusicplayer.utils.RenderingOptimizer.optimizeActivityRendering(RenderingOptimizer.kt:41)
                                                                                                    	at com.example.aimusicplayer.ui.main.MainActivity.onCreate(MainActivity.java:92)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:8342)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:8321)
                                                                                                    	at android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1417)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3626)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3782)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:101)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:138)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:95)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2307)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-25 16:53:53.950  4435-4435  tatementservice         com.android.statementservice         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:54.236   616-787   WifiHealthMonitor       system_server                        E   Hit PackageManager exception (Ask Gemini)
                                                                                                    android.content.pm.PackageManager$NameNotFoundException: No module info for package: com.android.wifi
                                                                                                    	at android.app.ApplicationPackageManager.getModuleInfo(ApplicationPackageManager.java:1187)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.getWifiStackVersion(WifiHealthMonitor.java:366)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.extractCurrentSoftwareBuildInfo(WifiHealthMonitor.java:587)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.postBootSwBuildCheck(WifiHealthMonitor.java:522)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.postBootDetectionHandler(WifiHealthMonitor.java:513)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.-$$Nest$mpostBootDetectionHandler(Unknown Source:0)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor$2.onAlarm(WifiHealthMonitor.java:190)
                                                                                                    	at android.app.AlarmManager$ListenerWrapper.run(AlarmManager.java:357)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 16:53:54.589  4137-4280  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 16:53:54.831  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085354.831487:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2afe43b9
2025-05-25 16:53:54.831  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085354.831682:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2afe43b9
2025-05-25 16:53:54.831  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085354.831908:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2afe43b9
2025-05-25 16:53:54.831  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085354.831952:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2afe43b9
2025-05-25 16:53:54.831  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085354.831974:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2afe43b9
2025-05-25 16:53:54.832  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085354.832005:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2afe43b9
2025-05-25 16:53:54.996  2130-2510  native                  com.google.android.gms               E  E0000 00:00:1748163234.996058    2510 document-store.cc:1359] Failed to update per-doc-data with usage report
2025-05-25 16:53:55.068  2130-2510  native                  com.google.android.gms               E  E0000 00:00:1748163235.068833    2510 document-store.cc:1359] Failed to update per-doc-data with usage report
2025-05-25 16:53:56.019  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085356.019641:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b2afec935
2025-05-25 16:53:56.019  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085356.019699:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b2afec935
2025-05-25 16:53:56.019  4368-4368  droid.bluetooth         com.google.android.bluetooth         E  [0525/085356.019745:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-25 16:53:56.172   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:56.172   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:56.172   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:56.615  4137-4137  PlayerControllerImpl    com.example.aimusicplayer            E  replaceAll: 没有有效的歌曲
2025-05-25 16:53:56.662   616-4072  WindowManager           system_server                        E  setOnBackInvokedCallback(): No window state for package:com.example.aimusicplayer
2025-05-25 16:53:57.059  4137-4137  PlayerControllerImpl    com.example.aimusicplayer            E  replaceAll: 没有有效的歌曲
2025-05-25 16:53:57.217  4596-4596  DEBUG                   crash_dump64                         E  failed to read process info: failed to open /proc/4368
2025-05-25 16:53:57.820   236-236   tombstoned              tombstoned                           E  Tombstone written to: tombstone_14
2025-05-25 16:53:57.915   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:57.916   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:53:57.937   616-753   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 16:53:58.192  2226-2519  RadioStationSyncImpl    com.google.android.carassistant      E  Error retrieving the OEM radio App's browse tree (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: resolveInfo is null
                                                                                                    	at izy.c(PG:28)
                                                                                                    	at jdj.a(PG:35)
                                                                                                    	at jcx.a(PG:164)
                                                                                                    	at uok.a(PG:202)
                                                                                                    	at uzf.a(PG:105)
                                                                                                    	at yap.a(PG:3)
                                                                                                    	at xzv.run(PG:19)
                                                                                                    	at yar.run(PG:5)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
2025-05-25 16:53:58.741  4137-4137  PlayerControllerImpl    com.example.aimusicplayer            E  replaceAll: 没有有效的歌曲
2025-05-25 16:53:58.825  4137-4137  PlayerControllerImpl    com.example.aimusicplayer            E  replaceAll: 没有有效的歌曲
2025-05-25 16:53:59.011  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:53:59.084  2226-2512  ProjectInfo             com.google.android.carassistant      E  failed writing project id into ProtoDataStore (Ask Gemini)
                                                                                                    java.util.concurrent.CancellationException: Task was cancelled.
                                                                                                    	at xxm.r(PG:33)
                                                                                                    	at xxm.get(PG:3)
                                                                                                    	at a.bB(PG:2)
                                                                                                    	at xos.B(PG:10)
                                                                                                    	at xzh.run(PG:24)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
2025-05-25 16:53:59.353  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.353542:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b29de43b9
2025-05-25 16:53:59.353  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.353640:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b29de43b9
2025-05-25 16:53:59.353  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.353755:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b29de43b9
2025-05-25 16:53:59.353  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.353785:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b29de43b9
2025-05-25 16:53:59.353  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.353812:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b29de43b9
2025-05-25 16:53:59.353  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.353840:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b29de43b9
2025-05-25 16:53:59.547  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.547309:ERROR:message_loop_thread.cc(103)] DoInThreadDelayed: message loop is null for thread bt_main_thread(-1), from pc:0x707b29dec935
2025-05-25 16:53:59.547  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.547365:ERROR:btu_task.cc(94)] do_in_main_thread: failed from pc:0x707b29dec935
2025-05-25 16:53:59.547  4673-4673  droid.bluetooth         com.google.android.bluetooth         E  [0525/085359.547524:ERROR:bta_sys_main.cc(171)] bta_sys_sendmsg: do_in_main_thread failed
2025-05-25 16:53:59.683   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:59.684   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:59.684   616-1723  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-25 16:53:59.855  2226-4727  chromium                com.google.android.carassistant      E  [0525/085359.781290:ERROR:variations_seed_loader.cc(37)] Seed missing signature.
2025-05-25 16:53:59.958  4736-4736  webview_service         com.google.android.webview           E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:00.323  4761-4761  DEBUG                   crash_dump64                         E  failed to read process info: failed to open /proc/4673
2025-05-25 16:54:00.892   236-236   tombstoned              tombstoned                           E  Tombstone written to: tombstone_15
2025-05-25 16:54:00.895   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:54:00.908   616-736   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 16:54:00.933  4783-4783  d.configupdater         com.google.android.configupdater     E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:01.014   616-753   BluetoothManagerService system_server                        E  getState() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.getState(IBluetooth.java:1536)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousGetState(BluetoothManagerService.java:892)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2908)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2895)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mwaitForState(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2370)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 16:54:01.015   616-753   BluetoothManagerService system_server                        E  waitForState [12] time out
2025-05-25 16:54:01.015   616-753   BluetoothManagerService system_server                        E  Unable to call disable() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.disable(IBluetooth.java:1563)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousDisable(BluetoothManagerService.java:856)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.handleDisable(BluetoothManagerService.java:2713)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mhandleDisable(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2371)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 16:54:01.015   616-753   BluetoothManagerService system_server                        E  getState() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.getState(IBluetooth.java:1536)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousGetState(BluetoothManagerService.java:892)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2908)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2895)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mwaitForState(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2372)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 16:54:01.015   616-753   BluetoothManagerService system_server                        E  waitForState [13, 16, 15, 14, 11, 10] time out
2025-05-25 16:54:01.018   616-753   BluetoothManagerService system_server                        E  Reach maximum retry to restart Bluetooth!
2025-05-25 16:54:01.018   616-753   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 16:54:01.021   616-616   BluetoothAdapter        system_server                        E  java.lang.RuntimeException: android.os.DeadObjectException (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at android.bluetooth.BluetoothAdapter.getStateInternal(BluetoothAdapter.java:1320)
                                                                                                    	at android.bluetooth.BluetoothAdapter.getLeState(BluetoothAdapter.java:1378)
                                                                                                    	at android.bluetooth.BluetoothAdapter.isLeEnabled(BluetoothAdapter.java:1157)
                                                                                                    	at com.android.server.companion.presence.BleCompanionDeviceScanner.checkBleState(BleCompanionDeviceScanner.java:157)
                                                                                                    	at com.android.server.companion.presence.BleCompanionDeviceScanner.-$$Nest$mcheckBleState(Unknown Source:0)
                                                                                                    	at com.android.server.companion.presence.BleCompanionDeviceScanner$1.onReceive(BleCompanionDeviceScanner.java:312)
                                                                                                    	at android.app.LoadedApk$ReceiverDispatcher$Args.lambda$getRunnable$0$android-app-LoadedApk$ReceiverDispatcher$Args(LoadedApk.java:1790)
                                                                                                    	at android.app.LoadedApk$ReceiverDispatcher$Args$$ExternalSyntheticLambda0.run(Unknown Source:2)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at com.android.server.SystemServer.run(SystemServer.java:962)
                                                                                                    	at com.android.server.SystemServer.main(SystemServer.java:647)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:914)
2025-05-25 16:54:01.041  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.047  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.052  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.058  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.071  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.073  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.077  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.091  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.095  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.104  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.106  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.108  4783-4783  ConfigUpdater           com.google.android.configupdater     E  ignoring update request
2025-05-25 16:54:01.239  4836-4836  timeinitializer         com...le.android.onetimeinitializer  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:01.272  2130-4837  PhenotypeRegOp          com.google.android.gms               E  Attempting to overwrite config package for com.google.android.gms.auth_account_auto#com.google.android.gms [CONTEXT service_id=231 ]
2025-05-25 16:54:01.493  4873-4873  ackageinstaller         com.google.android.packageinstaller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:01.625  4895-4895  id.partnersetup         com.google.android.partnersetup      E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:02.031  2130-4837  adservices              com.google.android.gms               E  Exception caught when modifying AdExtDtaService state! [CONTEXT service_id=261 ] (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Component class com.google.android.gms.adsidentity.service.AdServicesExtDataStorageService does not exist in com.google.android.gms
                                                                                                    	at android.os.Parcel.createExceptionOrNull(Parcel.java:3015)
                                                                                                    	at android.os.Parcel.createException(Parcel.java:2995)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2978)
                                                                                                    	at android.os.Parcel.readException(Parcel.java:2920)
                                                                                                    	at android.content.pm.IPackageManager$Stub$Proxy.setComponentEnabledSetting(IPackageManager.java:5881)
                                                                                                    	at android.app.ApplicationPackageManager.setComponentEnabledSetting(ApplicationPackageManager.java:3050)
                                                                                                    	at adui.m(:com.google.android.gms@*********@24.26.32 (230800-*********):30)
                                                                                                    	at aduj.M(:com.google.android.gms@*********@24.26.32 (230800-*********):12)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.d(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.b(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at zyx.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):136)
                                                                                                    	at com.google.android.gms.adsidentity.init.ModifyAdServicesExtDataStorageServiceStateIntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):22)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at lnq.run(:com.google.android.gms@*********@24.26.32 (230800-*********):70)
                                                                                                    	at lnp.run(:com.google.android.gms@*********@24.26.32 (230800-*********):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: android.os.RemoteException: Remote stack trace:
                                                                                                    	at com.android.server.pm.PackageManagerService.setEnabledSettings(PackageManagerService.java:3792)
                                                                                                    	at com.android.server.pm.PackageManagerService.-$$Nest$msetEnabledSettings(Unknown Source:0)
                                                                                                    	at com.android.server.pm.PackageManagerService$IPackageManagerImpl.setComponentEnabledSetting(PackageManagerService.java:5603)
                                                                                                    	at android.content.pm.IPackageManager$Stub.onTransact(IPackageManager.java:2931)
                                                                                                    	at com.android.server.pm.PackageManagerService$IPackageManagerImpl.onTransact(PackageManagerService.java:5997)
2025-05-25 16:54:02.072  2130-4831  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.mg.ui.main.MainActivity' is not available.
2025-05-25 16:54:02.076  2130-4831  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SettingsLoaderActivity' is not available.
2025-05-25 16:54:02.078  2130-4831  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.service.PurgeScreenDataService' is not available.
2025-05-25 16:54:02.078  2130-4831  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.ZeroPartyEntryPointActivity' is not available.
2025-05-25 16:54:02.091  2130-4831  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.PrivacyHubActivityControlsActivity' is not available.
2025-05-25 16:54:02.092  2130-4831  AccountSettings         com.google.android.gms               E  [ModuleInitializer] Component 'com.google.android.gms.accountsettings.ui.SafetyCenterActivityControlsActivity' is not available.
2025-05-25 16:54:02.199  2130-4916  JavaBinder              com.google.android.gms               E  !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
2025-05-25 16:54:02.390  4895-4895  GooglePartnerSetup      com.google.android.partnersetup      E  Phenotype client.register: true
2025-05-25 16:54:02.869   324-400   BpfHandler              netd                                 E  Unsupported protocol: 1
2025-05-25 16:54:02.938  2559-2866  Finsky                  com.android.vending                  E  [196] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:02.939  2559-2866  Finsky                  com.android.vending                  E  [196] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:02.939  2559-2866  Finsky                  com.android.vending                  E  [196] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:03.003  2559-2899  Finsky                  com.android.vending                  E  [205] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:03.069  1776-2375  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@72be013a, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@*********@24.26.32 (230800-*********):807)
                                                                                                    	at bncl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@*********@24.26.32 (230800-*********):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@*********@24.26.32 (230800-*********):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@*********@24.26.32 (230800-*********):1)
                                                                                                    	at auvl.run(:com.google.android.gms@*********@24.26.32 (230800-*********):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at adtm.c(:com.google.android.gms@*********@24.26.32 (230800-*********):50)
                                                                                                    	at adtm.run(:com.google.android.gms@*********@24.26.32 (230800-*********):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:05.107  2946-3170  Finsky                  com.android.vending                  E  [223] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:05.108  2946-3118  Finsky                  com.android.vending                  E  [220] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:05.108  2946-3118  Finsky                  com.android.vending                  E  [220] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:05.148  2946-3210  Finsky                  com.android.vending                  E  [236] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:05.906  1776-4644  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.lockbox failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@*********@24.26.32 (230800-*********):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@*********@24.26.32 (230800-*********):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@*********@24.26.32 (230800-*********):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@*********@24.26.32 (230800-*********):4)
                                                                                                    	at com.google.android.gms.lockbox.LockboxIntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):48)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@*********@24.26.32 (230800-*********):8)
                                                                                                    	at lnq.run(:com.google.android.gms@*********@24.26.32 (230800-*********):70)
                                                                                                    	at lnp.run(:com.google.android.gms@*********@24.26.32 (230800-*********):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@*********@24.26.32 (230800-*********):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:06.791  1569-4114  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 16:54:08.043  2559-2899  Finsky                  com.android.vending                  E  [205] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:10.199  2946-3210  Finsky                  com.android.vending                  E  [236] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:10.880   616-787   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-25 16:54:13.123  5082-5082  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:13.243  4137-4137  PlayerControllerImpl    com.example.aimusicplayer            E  replaceAll: 没有有效的歌曲
2025-05-25 16:54:15.351  5157-5157  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 16:54:16.307  1745-4370  WorkSourceUtil          com.google.android.gms               E  Could not find package: com.google.android.gms.westworld
2025-05-25 16:54:17.674  2559-2861  Finsky                  com.android.vending                  E  [192] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:17.675  2559-2861  Finsky                  com.android.vending                  E  [192] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:17.676  2559-2861  Finsky                  com.android.vending                  E  [192] obb.a(333): SCH: Job 37-32 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:18.719  2946-3170  Finsky                  com.android.vending                  E  [223] iuw.a(52): Unexpected android-id = 0
2025-05-25 16:54:18.719  2946-3170  Finsky                  com.android.vending                  E  [223] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:18.722  2946-3170  Finsky                  com.android.vending                  E  [223] obb.a(333): SCH: Job 37-31 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 16:54:36.480  4137-4280  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 16:54:46.803  1569-4115  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 16:54:53.859  1745-2667  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-25 16:54:56.400  4137-4280  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 16:55:03.199  4137-4137  PlayerControllerImpl    com.example.aimusicplayer            E  replaceAll: 没有有效的歌曲
2025-05-25 16:55:03.685  2130-2483  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-25 16:55:10.354   349-349   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-25 16:55:30.891   616-787   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-25 16:55:33.593  2559-3135  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-25 16:55:33.624  2559-2851  Finsky                  com.android.vending                  E  [188] lua.a(218): Error when retrieving FCM instance id
2025-05-25 16:55:36.146  2946-3314  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-25 16:55:36.261  2946-3170  Finsky                  com.android.vending                  E  [223] lua.a(218): Error when retrieving FCM instance id
2025-05-25 16:55:43.657   174-203   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))